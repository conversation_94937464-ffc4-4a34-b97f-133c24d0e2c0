import type { Context } from 'hono';
import { RichTextTabsService } from './service-new';
import {
  createContentTypeConfigRepository,
  createRichTextContentRepository,
} from './repository-new';
import {
  EntityTypeEnum,
  LanguageCodeEnum,
  createConfigRequest,
  updateConfigRequest,
  createContentRequest,
  // updateContentRequest,
  reorderConfigsRequest,
  batchUpdateStatusRequest,
  softDeleteRequest,
} from './schema';
import { RichTextValidators, ValidationError } from './validators';
import { getDB } from '@/infrastructure';
import { jsonSuccess, jsonError, validationError } from '@/utils/errorResponse';
import { recordLog } from '@/utils/auditLog';

/**
 * 富文本标签页管理控制器
 * 处理 HTTP 请求和响应
 */
export class RichTextTabsController {
  // ========================================
  // 配置管理 API
  // ========================================

  /**
   * 获取实体的活跃标签页配置
   * GET /api/rich-text/configs/{entityType}/{entityId}/{languageCode}
   */
  static async getActiveConfigs(c: Context) {
    const entityType = c.req.param('entityType');
    const entityId = c.req.param('entityId');
    const languageCode = c.req.param('languageCode');

    // 参数验证
    const entityTypeResult = EntityTypeEnum.safeParse(entityType);
    const languageCodeResult = LanguageCodeEnum.safeParse(languageCode);

    if (!entityTypeResult.success) {
      return jsonError(c, 10001, '无效的实体类型', 400);
    }

    if (!languageCodeResult.success) {
      return jsonError(c, 10001, '无效的语言代码', 400);
    }

    if (!entityId || entityId.trim() === '') {
      return jsonError(c, 10001, '实体ID不能为空', 400);
    }

    try {
      const service = this.createService(c);
      const configs = await service.getActiveConfigs(
        entityTypeResult.data,
        entityId.trim(),
        languageCodeResult.data
      );

      return jsonSuccess(c, '获取配置成功', configs);
    } catch (error) {
      console.error('获取配置失败:', error);
      return jsonError(c, 10003, '获取配置失败', 500);
    }
  }

  /**
   * 获取实体的所有标签页配置（管理界面）
   * GET /api/rich-text/configs/{entityType}/{entityId}/{languageCode}/all
   */
  static async getAllConfigs(c: Context) {
    const entityType = c.req.param('entityType');
    const entityId = c.req.param('entityId');
    const languageCode = c.req.param('languageCode');
    const includeDeleted = c.req.query('includeDeleted') === 'true';

    // 参数验证
    const entityTypeResult = EntityTypeEnum.safeParse(entityType);
    const languageCodeResult = LanguageCodeEnum.safeParse(languageCode);

    if (!entityTypeResult.success || !languageCodeResult.success) {
      return jsonError(c, 10001, '无效的参数', 400);
    }

    if (!entityId || entityId.trim() === '') {
      return jsonError(c, 10001, '实体ID不能为空', 400);
    }

    try {
      const service = this.createService(c);
      const configs = await service.getAllConfigs(
        entityTypeResult.data,
        entityId.trim(),
        languageCodeResult.data,
        includeDeleted
      );

      return jsonSuccess(c, '获取配置成功', configs);
    } catch (error) {
      console.error('获取配置失败:', error);
      return jsonError(c, 10003, '获取配置失败', 500);
    }
  }

  /**
   * 创建新的标签页配置
   * POST /api/rich-text/configs
   */
  static async createConfig(c: Context) {
    try {
      const body = await c.req.json();
      const validatedData = createConfigRequest.parse(body);

      // 业务规则验证
      const service = this.createService(c);
      const existingConfigs = await service.getAllConfigs(
        validatedData.entity_type,
        validatedData.entity_id,
        validatedData.language_code
      );

      const validation = await RichTextValidators.validateCreateConfig(
        validatedData,
        existingConfigs.map((c) => ({ key: c.key, sort_order: c.sort_order }))
      );

      if (!validation.isValid) {
        throw new ValidationError(validation.errors);
      }

      const result = await service.createConfig(validatedData);

      await recordLog(c, {
        action: 'CREATE_CONFIG',
        targetType: 'config',
        targetId: result.id,
        meta: {
          entityType: validatedData.entity_type,
          languageCode: validatedData.language_code,
          key: result.key, // 使用生成的 key
          label: validatedData.label,
        },
      });

      return jsonSuccess(c, '配置创建成功', result, 201);
    } catch (error) {
      return this.handleError(c, error, '创建配置失败');
    }
  }

  /**
   * 更新标签页配置
   * PUT /api/rich-text/configs/{id}
   */
  static async updateConfig(c: Context) {
    const id = c.req.param('id');

    if (!id) {
      return jsonError(c, 10001, '配置ID不能为空', 400);
    }

    try {
      const body = await c.req.json();
      const validatedData = updateConfigRequest.parse(body);

      const service = this.createService(c);
      const result = await service.updateConfig(id, validatedData);

      await recordLog(c, {
        action: 'UPDATE_CONFIG',
        targetType: 'config',
        targetId: id,
        meta: validatedData,
      });

      return jsonSuccess(c, '配置更新成功', result);
    } catch (error) {
      return this.handleError(c, error, '更新配置失败');
    }
  }

  /**
   * 软删除标签页配置
   * DELETE /api/rich-text/configs/{id}
   */
  static async deleteConfig(c: Context) {
    const id = c.req.param('id');

    if (!id) {
      return jsonError(c, 10001, '配置ID不能为空', 400);
    }

    try {
      const body = await c.req.json();
      const validatedData = softDeleteRequest.parse(body);

      const service = this.createService(c);
      const success = await service.deleteConfig(id, validatedData.deleted_by);

      if (!success) {
        return jsonError(c, 10002, '配置不存在或已删除', 404);
      }

      await recordLog(c, {
        action: 'DELETE_CONFIG',
        targetType: 'config',
        targetId: id,
        meta: { deletedBy: validatedData.deleted_by },
      });

      return jsonSuccess(c, '配置删除成功');
    } catch (error) {
      return this.handleError(c, error, '删除配置失败');
    }
  }

  /**
   * 恢复已删除的配置
   * POST /api/rich-text/configs/{id}/restore
   */
  static async restoreConfig(c: Context) {
    const id = c.req.param('id');

    if (!id) {
      return jsonError(c, 10001, '配置ID不能为空', 400);
    }

    try {
      const service = this.createService(c);
      const success = await service.restoreConfig(id);

      if (!success) {
        return jsonError(c, 10002, '配置不存在或未删除', 404);
      }

      await recordLog(c, {
        action: 'RESTORE_CONFIG',
        targetType: 'config',
        targetId: id,
      });

      return jsonSuccess(c, '配置恢复成功');
    } catch (error) {
      return this.handleError(c, error, '恢复配置失败');
    }
  }

  /**
   * 重新排序标签页配置
   * POST /api/rich-text/configs/reorder
   */
  static async reorderConfigs(c: Context) {
    try {
      const body = await c.req.json();
      const validatedData = reorderConfigsRequest.parse(body);

      const service = this.createService(c);
      await service.reorderConfigs(validatedData);

      await recordLog(c, {
        action: 'REORDER_CONFIGS',
        targetType: 'config',
        targetId: `${validatedData.entity_type}-${validatedData.language_code}`,
        meta: {
          entityType: validatedData.entity_type,
          languageCode: validatedData.language_code,
          orderCount: validatedData.orders.length,
        },
      });

      return jsonSuccess(c, '排序更新成功');
    } catch (error) {
      return this.handleError(c, error, '排序更新失败');
    }
  }

  /**
   * 批量更新配置状态
   * POST /api/rich-text/configs/batch-status
   */
  static async batchUpdateStatus(c: Context) {
    try {
      const body = await c.req.json();
      const validatedData = batchUpdateStatusRequest.parse(body);

      const service = this.createService(c);
      const affectedCount = await service.batchUpdateStatus(validatedData);

      await recordLog(c, {
        action: 'BATCH_UPDATE_STATUS',
        targetType: 'config',
        targetId: validatedData.ids.join(','),
        meta: {
          isActive: validatedData.is_active,
          affectedCount,
        },
      });

      return jsonSuccess(c, '状态更新成功', { affected_count: affectedCount });
    } catch (error) {
      return this.handleError(c, error, '状态更新失败');
    }
  }

  // ========================================
  // 内容管理 API
  // ========================================

  /**
   * 获取实体的完整标签页数据（配置+内容）
   * GET /api/rich-text/tabs/{entityType}/{entityId}/{languageCode}
   */
  static async getEntityTabs(c: Context) {
    const entityType = c.req.param('entityType');
    const entityId = c.req.param('entityId');
    const languageCode = c.req.param('languageCode');
    const includeInactive = c.req.query('includeInactive') === 'true';

    // 参数验证
    const entityTypeResult = EntityTypeEnum.safeParse(entityType);
    const languageCodeResult = LanguageCodeEnum.safeParse(languageCode);

    if (!entityTypeResult.success || !languageCodeResult.success) {
      return jsonError(c, 10001, '无效的参数', 400);
    }

    if (!entityId) {
      return jsonError(c, 10001, '实体ID不能为空', 400);
    }

    try {
      const service = this.createService(c);
      const tabs = await service.getEntityTabs(
        entityTypeResult.data,
        entityId,
        languageCodeResult.data,
        includeInactive
      );

      return jsonSuccess(c, '获取标签页数据成功', tabs);
    } catch (error) {
      console.error('获取标签页数据失败:', error);
      return jsonError(c, 10003, '获取标签页数据失败', 500);
    }
  }

  /**
   * 创建或更新内容
   * POST /api/rich-text/content
   */
  static async upsertContent(c: Context) {
    try {
      const body = await c.req.json();
      const validatedData = createContentRequest.parse(body);

      // 内容验证
      const validation =
        RichTextValidators.validateCreateContent(validatedData);
      if (!validation.isValid) {
        throw new ValidationError(validation.errors);
      }

      const service = this.createService(c);
      const result = await service.upsertContent(
        validatedData.entity_type,
        validatedData.entity_id,
        validatedData.language_code,
        validatedData.content_type,
        validatedData.content
      );

      await recordLog(c, {
        action: 'UPSERT_CONTENT',
        targetType: 'content',
        targetId: result.id,
        meta: {
          entityType: validatedData.entity_type,
          entityId: validatedData.entity_id,
          languageCode: validatedData.language_code,
          contentType: validatedData.content_type,
        },
      });

      return jsonSuccess(c, '内容保存成功', result, 201);
    } catch (error) {
      return this.handleError(c, error, '保存内容失败');
    }
  }

  /**
   * 批量创建/更新实体的多个内容
   * POST /api/rich-text/content/batch
   */
  static async batchUpsertContents(c: Context) {
    try {
      const body = await c.req.json();
      const { entity_type, entity_id, language_code, contents } = body;

      // 基础参数验证
      const entityTypeResult = EntityTypeEnum.safeParse(entity_type);
      const languageCodeResult = LanguageCodeEnum.safeParse(language_code);

      if (!entityTypeResult.success || !languageCodeResult.success) {
        return jsonError(c, 10001, '无效的参数', 400);
      }

      if (!entity_id || !contents || typeof contents !== 'object') {
        return jsonError(c, 10001, '参数不完整', 400);
      }

      const service = this.createService(c);
      const results = await service.batchUpsertContents(
        entityTypeResult.data,
        entity_id,
        languageCodeResult.data,
        contents
      );

      await recordLog(c, {
        action: 'BATCH_UPSERT_CONTENTS',
        targetType: 'content',
        targetId: entity_id,
        meta: {
          entityType: entity_type,
          entityId: entity_id,
          languageCode: language_code,
          contentTypes: Object.keys(contents),
          successCount: results.length,
        },
      });

      return jsonSuccess(c, '批量保存成功', results);
    } catch (error) {
      return this.handleError(c, error, '批量保存失败');
    }
  }

  // ========================================
  // 辅助方法
  // ========================================

  /**
   * 创建服务实例
   */
  private static createService(c: Context): RichTextTabsService {
    const db = getDB(c);
    const configRepo = createContentTypeConfigRepository(db);
    const contentRepo = createRichTextContentRepository(db);
    return new RichTextTabsService(configRepo, contentRepo);
  }

  /**
   * 统一错误处理
   */
  private static handleError(c: Context, error: any, defaultMessage: string) {
    if (error instanceof ValidationError) {
      return validationError(c, { errors: error.errors.join(', ') });
    }

    if (error?.name === 'ZodError') {
      return validationError(c, { error: error.message });
    }

    console.error(defaultMessage + ':', error);
    return jsonError(c, 10003, defaultMessage, 500);
  }
}
