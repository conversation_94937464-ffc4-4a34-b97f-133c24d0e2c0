/**
 * Rich Text 内容初始化工具
 * 在创建新的 event/venue 时自动初始化富文本内容记录
 */

// D1Database 类型现在在全局作用域中可用
import { v4 as uuidv4 } from 'uuid';
import type { EntityType, LanguageCode } from './schema';

// 默认的空富文本内容（Tiptap JSON 格式）
const DEFAULT_EMPTY_CONTENT = JSON.stringify({
  type: 'doc',
  content: [
    {
      type: 'paragraph',
      content: [
        {
          type: 'text',
          text: '',
        },
      ],
    },
  ],
});

/**
 * 为新创建的实体初始化富文本内容
 *
 * @param db 数据库连接
 * @param entityType 实体类型 ('event' | 'venue')
 * @param entityId 实体ID
 * @returns 创建的内容记录数量
 */
export async function initializeRichTextContents(
  db: D1Database,
  entityType: EntityType,
  entityId: string
): Promise<{
  created: number;
  skipped: number;
  errors: string[];
}> {
  const languages: LanguageCode[] = ['en', 'zh', 'ja'];
  let createdCount = 0;
  let skippedCount = 0;
  const errors: string[] = [];

  try {
    // 1. 获取该实体类型的预设模板配置
    const presetConfigsStmt = db.prepare(`
      SELECT id, entity_type, language_code, key, label, placeholder, icon, sort_order, is_active
      FROM content_type_configs
      WHERE entity_type = ?
        AND entity_id IS NULL
        AND is_active = 1
        AND is_preset = 1
        AND deleted_at IS NULL
      ORDER BY language_code, sort_order
    `);

    const presetConfigsResult = await presetConfigsStmt.bind(entityType).all();
    const presetConfigs = presetConfigsResult.results as Array<{
      id: string;
      entity_type: string;
      language_code: string;
      key: string;
      label: string;
      placeholder: string | null;
      icon: string | null;
      sort_order: number;
      is_active: number;
    }>;

    if (presetConfigs.length === 0) {
      console.warn(
        `[RichText] 没有找到 ${entityType} 的预设配置，跳过内容初始化`
      );
      return { created: 0, skipped: 0, errors: ['没有找到预设配置'] };
    }

    // 2. 为该实体实例复制预设配置
    const now = new Date().toISOString();

    for (const presetConfig of presetConfigs) {
      try {
        // 检查是否已存在该实体的配置
        const existingConfigStmt = db.prepare(`
          SELECT id FROM content_type_configs
          WHERE entity_type = ? AND entity_id = ? AND language_code = ? AND key = ?
        `);

        const existingConfig = await existingConfigStmt
          .bind(
            entityType,
            entityId,
            presetConfig.language_code,
            presetConfig.key
          )
          .first();

        if (existingConfig) {
          skippedCount++;
          continue;
        }

        // 创建实体专属的配置记录
        const configId = uuidv4();
        const insertConfigStmt = db.prepare(`
          INSERT INTO content_type_configs (
            id, entity_type, entity_id, language_code, key, label, placeholder, icon,
            sort_order, is_active, is_preset, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        await insertConfigStmt
          .bind(
            configId,
            entityType,
            entityId,
            presetConfig.language_code,
            presetConfig.key,
            presetConfig.label,
            presetConfig.placeholder,
            presetConfig.icon,
            presetConfig.sort_order,
            1, // is_active = true
            0, // is_preset = false (实例配置不是预设)
            now,
            now
          )
          .run();

        console.log(
          `[RichText] 创建配置记录: ${entityType}/${entityId}/${presetConfig.language_code}/${presetConfig.key}`
        );
      } catch (error) {
        const errorMsg = `创建配置失败: ${presetConfig.language_code}/${presetConfig.key} - ${error}`;
        console.error(`[RichText] ${errorMsg}`);
        errors.push(errorMsg);
        continue;
      }
    }

    // 3. 为每个配置创建内容记录
    for (const presetConfig of presetConfigs) {
      try {
        // 检查是否已存在内容记录
        const existingContentStmt = db.prepare(`
          SELECT id FROM rich_text_contents
          WHERE entity_type = ? AND entity_id = ? AND language_code = ? AND content_type = ?
        `);

        const existingContent = await existingContentStmt
          .bind(
            entityType,
            entityId,
            presetConfig.language_code,
            presetConfig.key
          )
          .first();

        if (existingContent) {
          continue; // 内容已存在，跳过
        }

        // 创建新的内容记录
        const contentId = uuidv4();
        const insertContentStmt = db.prepare(`
          INSERT INTO rich_text_contents (
            id, entity_type, entity_id, language_code, content_type, content, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `);

        await insertContentStmt
          .bind(
            contentId,
            entityType,
            entityId,
            presetConfig.language_code,
            presetConfig.key,
            DEFAULT_EMPTY_CONTENT,
            now,
            now
          )
          .run();

        createdCount++;
        console.log(
          `[RichText] 创建内容记录: ${entityType}/${entityId}/${presetConfig.language_code}/${presetConfig.key}`
        );
      } catch (error) {
        const errorMsg = `创建内容记录失败: ${presetConfig.language_code}/${presetConfig.key} - ${error}`;
        errors.push(errorMsg);
        console.error(`[RichText] ${errorMsg}`);
      }
    }

    if (createdCount > 0) {
      console.log(
        `[RichText] 实体 ${entityType}:${entityId} 富文本内容初始化完成: 创建 ${createdCount} 条，跳过 ${skippedCount} 条`
      );
    }

    return {
      created: createdCount,
      skipped: skippedCount,
      errors,
    };
  } catch (error) {
    const errorMsg = `富文本内容初始化失败: ${error}`;
    console.error(`[RichText] ${errorMsg}`);
    return {
      created: 0,
      skipped: 0,
      errors: [errorMsg],
    };
  }
}

/**
 * 检查实体的富文本内容完整性
 *
 * @param db 数据库连接
 * @param entityType 实体类型
 * @param entityId 实体ID
 * @returns 完整性检查结果
 */
export async function checkRichTextContentIntegrity(
  db: D1Database,
  entityType: EntityType,
  entityId: string
): Promise<{
  isComplete: boolean;
  expectedCount: number;
  actualCount: number;
  missing: Array<{ language_code: string; content_type: string }>;
}> {
  try {
    // 获取预期的配置数量
    const expectedStmt = db.prepare(`
      SELECT language_code, key as content_type
      FROM content_type_configs
      WHERE entity_type = ? AND entity_id = ? AND is_active = 1 AND deleted_at IS NULL
    `);

    const expectedResult = await expectedStmt.bind(entityType, entityId).all();
    const expectedConfigs = expectedResult.results as Array<{
      language_code: string;
      content_type: string;
    }>;

    // 获取实际的内容数量
    const actualStmt = db.prepare(`
      SELECT language_code, content_type
      FROM rich_text_contents 
      WHERE entity_type = ? AND entity_id = ?
    `);

    const actualResult = await actualStmt.bind(entityType, entityId).all();
    const actualContents = actualResult.results as Array<{
      language_code: string;
      content_type: string;
    }>;

    // 找出缺失的内容
    const missing = expectedConfigs.filter(
      (expected) =>
        !actualContents.some(
          (actual) =>
            actual.language_code === expected.language_code &&
            actual.content_type === expected.content_type
        )
    );

    return {
      isComplete: missing.length === 0,
      expectedCount: expectedConfigs.length,
      actualCount: actualContents.length,
      missing,
    };
  } catch (error) {
    console.error(`[RichText] 完整性检查失败: ${error}`);
    return {
      isComplete: false,
      expectedCount: 0,
      actualCount: 0,
      missing: [],
    };
  }
}

/**
 * 修复缺失的富文本内容
 *
 * @param db 数据库连接
 * @param entityType 实体类型
 * @param entityId 实体ID
 * @returns 修复结果
 */
export async function repairRichTextContents(
  db: D1Database,
  entityType: EntityType,
  entityId: string
): Promise<{
  repaired: number;
  errors: string[];
}> {
  const integrity = await checkRichTextContentIntegrity(
    db,
    entityType,
    entityId
  );

  if (integrity.isComplete) {
    return { repaired: 0, errors: [] };
  }

  let repairedCount = 0;
  const errors: string[] = [];
  const now = new Date().toISOString();

  for (const missing of integrity.missing) {
    try {
      const contentId = uuidv4();
      const insertStmt = db.prepare(`
        INSERT INTO rich_text_contents (
          id, entity_type, entity_id, language_code, content_type, content, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `);

      await insertStmt
        .bind(
          contentId,
          entityType,
          entityId,
          missing.language_code,
          missing.content_type,
          DEFAULT_EMPTY_CONTENT,
          now,
          now
        )
        .run();

      repairedCount++;
      console.log(
        `[RichText] 修复内容记录: ${entityType}/${entityId}/${missing.language_code}/${missing.content_type}`
      );
    } catch (error) {
      const errorMsg = `修复失败: ${missing.language_code}/${missing.content_type} - ${error}`;
      errors.push(errorMsg);
      console.error(`[RichText] ${errorMsg}`);
    }
  }

  return { repaired: repairedCount, errors };
}
