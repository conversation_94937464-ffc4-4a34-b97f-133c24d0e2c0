import type {
  ContentTypeConfig,
  RichTextContent,
  EntityType,
  LanguageCode,
  CreateConfigRequest,
  UpdateConfigRequest,
  // CreateContentRequest,
  // UpdateContentRequest,
  TabWithContent,
  EntityTabsResponse,
  ReorderConfigsRequest,
  BatchUpdateStatusRequest,
  // SoftDeleteRequest,
} from './schema';
import type {
  ContentTypeConfigRepository,
  RichTextContentRepository,
} from './repository-new';
import { RichTextTabsCache } from './cache';
import { generateUniqueKey } from './key-generator';

/**
 * 富文本标签页管理服务
 * 处理业务逻辑和数据协调
 */
export class RichTextTabsService {
  constructor(
    private configRepo: ContentTypeConfigRepository,
    private contentRepo: RichTextContentRepository,
    private cache?: RichTextTabsCache
  ) {}

  // ========================================
  // 配置管理相关方法
  // ========================================

  /**
   * 获取实体的活跃标签页配置
   */
  async getActiveConfigs(
    entityType: EntityType,
    entityId: string,
    languageCode: LanguageCode
  ): Promise<ContentTypeConfig[]> {
    // 尝试从缓存获取
    if (this.cache) {
      const cached = await this.cache.getActiveConfigs(
        entityType,
        entityId,
        languageCode
      );
      if (cached) {
        return cached;
      }
    }

    // 从数据库获取
    const configs = await this.configRepo.getActiveConfigs(
      entityType,
      entityId,
      languageCode
    );

    // 设置缓存
    if (this.cache) {
      await this.cache.setActiveConfigs(
        entityType,
        entityId,
        languageCode,
        configs
      );
    }

    return configs;
  }

  /**
   * 获取实体的所有标签页配置（管理界面用）
   */
  async getAllConfigs(
    entityType: EntityType,
    entityId: string,
    languageCode: LanguageCode,
    includeDeleted: boolean = false
  ): Promise<ContentTypeConfig[]> {
    return await this.configRepo.getAllConfigs(
      entityType,
      entityId,
      languageCode,
      includeDeleted
    );
  }

  /**
   * 创建新的标签页配置（强制后端生成 key）
   */
  async createConfig(data: CreateConfigRequest): Promise<ContentTypeConfig> {
    // 获取现有配置以检查重复和生成排序
    const existingConfigs = await this.configRepo.getAllConfigs(
      data.entity_type,
      data.entity_id,
      data.language_code
    );

    const existingKeys = existingConfigs.map((c) => c.key);

    // 强制后端生成 key（实体关联 + UUID 方案）
    const finalKey = await generateUniqueKey(
      data.label,
      data.entity_type,
      data.language_code,
      existingKeys,
      undefined, // 不支持自定义 key
      data.entity_id // 使用实体 ID
    );

    // 如果没有指定排序值，设置为最大值+1
    if (data.sort_order === undefined) {
      const maxOrder = Math.max(
        ...existingConfigs.map((c) => c.sort_order),
        -1
      );
      data.sort_order = maxOrder + 1;
    }

    // 创建配置数据，确保 key 字段存在
    const configData: CreateConfigRequest & { key: string } = {
      ...data,
      key: finalKey,
    };

    return await this.configRepo.createConfig(configData);
  }

  /**
   * 更新标签页配置
   */
  async updateConfig(
    id: string,
    data: UpdateConfigRequest
  ): Promise<ContentTypeConfig> {
    const config = await this.configRepo.getConfigById(id);
    if (!config) {
      throw new Error('配置不存在');
    }

    if (config.is_preset) {
      // 预设配置只允许更新部分字段
      const allowedFields: (keyof UpdateConfigRequest)[] = [
        'label',
        'placeholder',
        'icon',
      ];
      const restrictedFields = Object.keys(data).filter(
        (key) => !allowedFields.includes(key as keyof UpdateConfigRequest)
      );

      if (restrictedFields.length > 0) {
        throw new Error(
          `预设配置不允许修改字段: ${restrictedFields.join(', ')}`
        );
      }
    }

    return await this.configRepo.updateConfig(id, data);
  }

  /**
   * 软删除标签页配置
   */
  async deleteConfig(id: string, deletedBy: string): Promise<boolean> {
    const config = await this.configRepo.getConfigById(id);
    if (!config) {
      throw new Error('配置不存在');
    }

    if (config.is_preset) {
      throw new Error('预设配置不能删除');
    }

    return await this.configRepo.softDeleteConfig(id, deletedBy);
  }

  /**
   * 恢复已删除的配置
   */
  async restoreConfig(id: string): Promise<boolean> {
    return await this.configRepo.restoreConfig(id);
  }

  /**
   * 重新排序标签页配置
   */
  async reorderConfigs(data: ReorderConfigsRequest): Promise<void> {
    // 验证所有ID都存在且属于指定的实体类型和语言
    const configs = await this.configRepo.getAllConfigs(
      data.entity_type,
      data.language_code
    );

    const configIds = new Set(configs.map((c) => c.id));
    const invalidIds = data.orders.filter((order) => !configIds.has(order.id));

    if (invalidIds.length > 0) {
      throw new Error(
        `无效的配置ID: ${invalidIds.map((o) => o.id).join(', ')}`
      );
    }

    await this.configRepo.reorderConfigs(
      data.entity_type,
      data.language_code,
      data.orders
    );
  }

  /**
   * 批量更新配置状态
   */
  async batchUpdateStatus(data: BatchUpdateStatusRequest): Promise<number> {
    // 检查是否包含预设配置
    const configs = await Promise.all(
      data.ids.map((id) => this.configRepo.getConfigById(id))
    );

    const presetConfigs = configs.filter((config) => config?.is_preset);
    if (presetConfigs.length > 0 && !data.is_active) {
      throw new Error('预设配置不能被禁用');
    }

    return await this.configRepo.batchUpdateStatus(data.ids, data.is_active);
  }

  // ========================================
  // 内容管理相关方法
  // ========================================

  /**
   * 获取实体的所有内容
   */
  async getContentsByEntity(
    entityType: EntityType,
    entityId: string,
    languageCode: LanguageCode
  ): Promise<RichTextContent[]> {
    return await this.contentRepo.getContentsByEntity(
      entityType,
      entityId,
      languageCode
    );
  }

  /**
   * 获取特定内容
   */
  async getContent(
    entityType: EntityType,
    entityId: string,
    languageCode: LanguageCode,
    contentType: string
  ): Promise<RichTextContent | null> {
    return await this.contentRepo.getContent(
      entityType,
      entityId,
      languageCode,
      contentType
    );
  }

  /**
   * 创建或更新内容
   */
  async upsertContent(
    entityType: EntityType,
    entityId: string,
    languageCode: LanguageCode,
    contentType: string,
    content: string
  ): Promise<RichTextContent> {
    // 验证内容类型是否存在且活跃
    const config = await this.configRepo.getConfigByKey(
      entityType,
      entityId,
      languageCode,
      contentType
    );

    if (!config) {
      throw new Error(`内容类型 "${contentType}" 不存在`);
    }

    if (!config.is_active) {
      throw new Error(`内容类型 "${contentType}" 已被禁用`);
    }

    return await this.contentRepo.upsertContent(
      entityType,
      entityId,
      languageCode,
      contentType,
      content
    );
  }

  /**
   * 删除内容
   */
  async deleteContent(id: string): Promise<boolean> {
    return await this.contentRepo.deleteContent(id);
  }

  /**
   * 删除实体的所有内容
   */
  async deleteContentsByEntity(
    entityType: EntityType,
    entityId: string
  ): Promise<number> {
    return await this.contentRepo.deleteContentsByEntity(entityType, entityId);
  }

  // ========================================
  // 组合查询方法
  // ========================================

  /**
   * 获取实体的完整标签页数据（配置+内容）
   */
  async getEntityTabs(
    entityType: EntityType,
    entityId: string,
    languageCode: LanguageCode,
    includeInactive: boolean = false
  ): Promise<EntityTabsResponse> {
    // 获取实体级别的配置
    const configs = includeInactive
      ? await this.configRepo.getAllConfigs(entityType, entityId, languageCode)
      : await this.configRepo.getActiveConfigs(
          entityType,
          entityId,
          languageCode
        );

    // 获取内容
    const contents = await this.contentRepo.getContentsByEntity(
      entityType,
      entityId,
      languageCode
    );

    // 创建内容映射
    const contentMap = new Map<string, RichTextContent>();
    contents.forEach((content) => {
      contentMap.set(content.content_type, content);
    });

    // 组合数据
    const tabs: TabWithContent[] = configs.map((config) => ({
      config,
      content: contentMap.get(config.key),
    }));

    return {
      entity_type: entityType,
      entity_id: entityId,
      language_code: languageCode,
      tabs,
    };
  }

  /**
   * 批量创建/更新实体的多个内容
   */
  async batchUpsertContents(
    entityType: EntityType,
    entityId: string,
    languageCode: LanguageCode,
    contents: Record<string, string>
  ): Promise<RichTextContent[]> {
    const results: RichTextContent[] = [];

    for (const [contentType, content] of Object.entries(contents)) {
      if (content.trim()) {
        try {
          const result = await this.upsertContent(
            entityType,
            entityId,
            languageCode,
            contentType,
            content
          );
          results.push(result);
        } catch (error) {
          console.warn(
            `Failed to upsert content for type ${contentType}:`,
            error
          );
          // 继续处理其他内容，不中断整个批量操作
        }
      }
    }

    return results;
  }

  /**
   * 获取最近更新的内容
   */
  async getRecentlyUpdatedContents(
    limit: number = 10
  ): Promise<RichTextContent[]> {
    return await this.contentRepo.getRecentlyUpdatedContents(limit);
  }

  // ========================================
  // 数据验证和业务规则
  // ========================================

  /**
   * 验证实体是否存在（需要根据实际的实体服务实现）
   */
  private async validateEntityExists(
    _entityType: EntityType,
    _entityId: string
  ): Promise<boolean> {
    // TODO: 实现实际的实体验证逻辑
    // 这里应该调用相应的 Events 或 Venues 服务来验证实体是否存在
    return true;
  }

  /**
   * 检查用户是否有权限操作指定实体
   */
  private async checkPermission(
    _userId: string,
    _entityType: EntityType,
    _entityId: string,
    _action: 'read' | 'write' | 'delete'
  ): Promise<boolean> {
    // TODO: 实现权限检查逻辑
    return true;
  }
}
