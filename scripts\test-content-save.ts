#!/usr/bin/env tsx

/**
 * 测试内容保存功能
 */

async function testContentSave() {
  console.log('🧪 测试内容保存功能...');

  try {
    const testData = {
      entity_type: 'event',
      entity_id: 'test_event_1',
      language_code: 'zh',
      content_type: 'introduction',
      content: JSON.stringify({
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            attrs: { textAlign: null },
            content: [
              {
                type: 'text',
                text: '测试内容 - 修复后的保存功能'
              }
            ]
          }
        ]
      })
    };

    console.log('📝 发送保存请求...');
    console.log('请求数据:', JSON.stringify(testData, null, 2));

    const response = await fetch('http://localhost:8787/rich-text-tabs/content', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    });

    console.log(`📊 响应状态: ${response.status} ${response.statusText}`);

    const responseData = await response.json();
    console.log('📄 响应数据:', JSON.stringify(responseData, null, 2));

    if (response.ok) {
      console.log('✅ 内容保存成功！');
      
      // 验证保存的内容
      if (responseData.data) {
        console.log('📋 保存的内容信息:');
        console.log(`   - ID: ${responseData.data.id}`);
        console.log(`   - 实体: ${responseData.data.entity_type}/${responseData.data.entity_id}`);
        console.log(`   - 语言: ${responseData.data.language_code}`);
        console.log(`   - 类型: ${responseData.data.content_type}`);
        console.log(`   - 内容长度: ${responseData.data.content.length} 字符`);
      }
    } else {
      console.log('❌ 内容保存失败！');
      console.log('错误信息:', responseData.message || '未知错误');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
testContentSave().then(() => {
  console.log('\n🎉 内容保存测试完成！');
}).catch(console.error);
